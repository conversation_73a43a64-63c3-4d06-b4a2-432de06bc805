import api from './api.js';
import LogService from './logService.js';

// 单独导出getTaskById函数，用于组件直接导入
export const getTaskById = async (id) => {
  // 记录开始获取任务
  LogService.debug(`正在获取任务数据，ID: ${id}`);

  try {
    // 尝试使用shared端点获取任务（适用于非登录用户）
    const response = await api.get(`/tasks/shared/${id}`);
    LogService.debug(`通过共享端点成功获取任务: ${id}`);
    return response;
  } catch (error) {
    // 记录第一个端点失败
    LogService.debug(`共享端点获取任务失败: ${id}，尝试认证端点`, error);

    // 如果shared端点失败，尝试使用普通端点（需要登录）
    try {
      const response = await api.get(`/tasks/${id}`);
      LogService.debug(`通过认证端点成功获取任务: ${id}`);
      return response;
    } catch (secondError) {
      // 详细记录错误
      LogService.error(`无法获取任务: ${id}，所有端点都失败`, {
        sharedEndpointError: error.message,
        authEndpointError: secondError.message,
        status: secondError.response?.status,
        statusText: secondError.response?.statusText
      });

      // 如果两个端点都失败，抛出第二个错误
      throw secondError;
    }
  }
};

export const taskService = {
  // 获取任务列表
  getTasks: async (params = {}) => {
    return await api.get('/tasks', { params });
  },

  // 获取任务详情
  getTask: async (id) => {
    return await api.get(`/tasks/${id}`);
  },

  // 创建任务
  createTask: async (taskData) => {
    return await api.post('/tasks', taskData);
  },

  // 更新任务
  updateTask: async (id, taskData) => {
    return await api.put(`/tasks/${id}`, taskData);
  },

  // 删除任务
  deleteTask: async (id) => {
    return await api.delete(`/tasks/${id}`);
  },

  // 生成分享链接
  shareTask: async (id) => {
    return await api.post(`/tasks/${id}/share`);
  }
};

export default taskService;
