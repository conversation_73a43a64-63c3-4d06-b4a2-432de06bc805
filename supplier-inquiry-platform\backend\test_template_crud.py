#!/usr/bin/env python3
"""
测试Template CRUD功能
"""
import sys
import os
sys.path.append(os.path.dirname(__file__))

import uuid
from sqlalchemy.orm import Session

try:
    from app.db.session import SessionLocal
    from app.models.template import Template
    from app.models.user import User
    from app.models.company import Company
    from app.crud.template import (
        create_template, get_template, get_templates, 
        update_template, delete_template, get_accessible_templates
    )
    from app.schemas.template import TemplateCreate, TemplateUpdate
    
    print("✅ 所有模块导入成功")
    
    # 创建数据库会话
    db = SessionLocal()
    
    try:
        # 检查是否有用户
        user = db.query(User).first()
        if not user:
            print("❌ 数据库中没有用户，请先运行应用程序初始化数据库")
            sys.exit(1)
        
        print(f"✅ 找到测试用户: {user.username} (Level: {user.level})")
        
        # 测试创建模板
        print("\n📝 测试创建模板:")
        template_data = TemplateCreate(
            name="测试模板",
            description="这是一个测试模板",
            type="inquiry",
            category="测试分类",
            tags={"tag1": "value1", "tag2": "value2"},
            content={"field1": "文本字段", "field2": "数字字段"},
            scope="private",
            company_id=user.company_id
        )
        
        created_template = create_template(db=db, template_in=template_data, creator_id=user.id)
        print(f"  ✓ 模板创建成功: ID={created_template.id}, 名称={created_template.name}")
        
        # 测试获取模板
        print("\n📖 测试获取模板:")
        retrieved_template = get_template(db=db, template_id=created_template.id)
        if retrieved_template:
            print(f"  ✓ 模板获取成功: {retrieved_template.name}")
        else:
            print("  ❌ 模板获取失败")
        
        # 测试获取模板列表
        print("\n📋 测试获取模板列表:")
        templates = get_templates(db=db, limit=10)
        print(f"  ✓ 获取到 {len(templates)} 个模板")
        
        # 测试获取用户可访问的模板
        print("\n🔐 测试权限控制:")
        accessible_templates = get_accessible_templates(db=db, user=user, limit=10)
        print(f"  ✓ 用户可访问 {len(accessible_templates)} 个模板")
        
        # 测试更新模板
        print("\n✏️ 测试更新模板:")
        update_data = TemplateUpdate(
            name="更新后的测试模板",
            description="这是更新后的描述"
        )
        updated_template = update_template(db=db, template=created_template, template_in=update_data)
        print(f"  ✓ 模板更新成功: {updated_template.name}")
        
        # 测试搜索功能
        print("\n🔍 测试搜索功能:")
        search_results = get_templates(db=db, name="测试", limit=10)
        print(f"  ✓ 搜索到 {len(search_results)} 个包含'测试'的模板")
        
        # 测试按类型筛选
        type_results = get_templates(db=db, type="inquiry", limit=10)
        print(f"  ✓ 找到 {len(type_results)} 个inquiry类型的模板")
        
        # 测试软删除
        print("\n🗑️ 测试软删除:")
        deleted_template = delete_template(db=db, template=created_template)
        print(f"  ✓ 模板软删除成功: is_active={deleted_template.is_active}")
        
        # 验证软删除后的查询
        active_templates = get_templates(db=db, is_active=True, limit=10)
        inactive_templates = get_templates(db=db, is_active=False, limit=10)
        print(f"  ✓ 活跃模板: {len(active_templates)}, 已删除模板: {len(inactive_templates)}")
        
        print("\n✅ Template CRUD功能测试完成！")
        
        # 测试总结
        print("\n📊 测试总结:")
        print("  ✓ 模板创建功能正常")
        print("  ✓ 模板读取功能正常")
        print("  ✓ 模板更新功能正常")
        print("  ✓ 模板删除功能正常")
        print("  ✓ 搜索筛选功能正常")
        print("  ✓ 权限控制功能正常")
        print("  ✓ 软删除功能正常")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()
        
except Exception as e:
    print(f"❌ 初始化失败: {e}")
    import traceback
    traceback.print_exc()
