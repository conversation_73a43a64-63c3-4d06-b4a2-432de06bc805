import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  Button, 
  Table, 
  Card, 
  Space, 
  Input, 
  Select, 
  Tag, 
  Modal, 
  Form, 
  message,
  Spin,
  Pagination
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  CopyOutlined, 
  SearchOutlined 
} from '@ant-design/icons';

// 导入Redux actions和selectors
import {
  fetchTemplates,
  fetchTemplate,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  copyTemplate,
  searchTemplatesByTags,
  clearCurrentTemplate,
  clearError,
  setFilters,
  resetFilters,
  setPagination,
  selectTemplates,
  selectCurrentTemplate,
  selectTemplateLoading,
  selectTemplateError,
  selectTemplatePagination,
  selectTemplateFilters,
  selectTemplateOperations
} from '../store/slices/templateSlice';

const { Option } = Select;
const { Search } = Input;

/**
 * Template管理组件示例
 * 展示如何使用Template Redux状态管理
 */
const TemplateExample = () => {
  const dispatch = useDispatch();
  
  // 从Redux store获取状态
  const templates = useSelector(selectTemplates);
  const currentTemplate = useSelector(selectCurrentTemplate);
  const loading = useSelector(selectTemplateLoading);
  const error = useSelector(selectTemplateError);
  const pagination = useSelector(selectTemplatePagination);
  const filters = useSelector(selectTemplateFilters);
  const operations = useSelector(selectTemplateOperations);
  
  // 本地状态
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalType, setModalType] = useState('create'); // 'create' | 'edit' | 'view'
  const [form] = Form.useForm();

  // 组件挂载时获取模板列表
  useEffect(() => {
    dispatch(fetchTemplates({ 
      page: pagination.page, 
      size: pagination.size,
      ...filters 
    }));
  }, [dispatch, pagination.page, pagination.size, filters]);

  // 错误处理
  useEffect(() => {
    if (error) {
      message.error(error);
      dispatch(clearError());
    }
  }, [error, dispatch]);

  // 表格列定义
  const columns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Button 
          type="link" 
          onClick={() => handleViewTemplate(record.id)}
        >
          {text}
        </Button>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => {
        const typeMap = {
          inquiry: { color: 'blue', text: '询价' },
          task: { color: 'green', text: '任务' },
          field: { color: 'orange', text: '字段' }
        };
        const config = typeMap[type] || { color: 'default', text: type };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '范围',
      dataIndex: 'scope',
      key: 'scope',
      render: (scope) => {
        const scopeMap = {
          private: { color: 'red', text: '私有' },
          department: { color: 'orange', text: '部门' },
          company: { color: 'blue', text: '公司' },
          public: { color: 'green', text: '公开' }
        };
        const config = scopeMap[scope] || { color: 'default', text: scope };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => handleEditTemplate(record)}
          >
            编辑
          </Button>
          <Button 
            icon={<CopyOutlined />} 
            size="small"
            loading={operations.copying}
            onClick={() => handleCopyTemplate(record.id)}
          >
            复制
          </Button>
          <Button 
            icon={<DeleteOutlined />} 
            size="small" 
            danger
            loading={operations.deleting}
            onClick={() => handleDeleteTemplate(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 处理函数
  const handleViewTemplate = (id) => {
    dispatch(fetchTemplate(id));
    setModalType('view');
    setIsModalVisible(true);
  };

  const handleEditTemplate = (template) => {
    dispatch(fetchTemplate(template.id));
    setModalType('edit');
    setIsModalVisible(true);
    form.setFieldsValue(template);
  };

  const handleCreateTemplate = () => {
    setModalType('create');
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleCopyTemplate = (id) => {
    Modal.confirm({
      title: '复制模板',
      content: '确定要复制这个模板吗？',
      onOk: () => {
        dispatch(copyTemplate({ 
          id, 
          newName: `${templates.find(t => t.id === id)?.name} - 副本` 
        }));
      }
    });
  };

  const handleDeleteTemplate = (id) => {
    Modal.confirm({
      title: '删除模板',
      content: '确定要删除这个模板吗？此操作不可恢复。',
      okType: 'danger',
      onOk: () => {
        dispatch(deleteTemplate(id));
      }
    });
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      if (modalType === 'create') {
        dispatch(createTemplate(values));
      } else if (modalType === 'edit') {
        dispatch(updateTemplate({ 
          id: currentTemplate.id, 
          templateData: values 
        }));
      }
      setIsModalVisible(false);
      form.resetFields();
    });
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
    dispatch(clearCurrentTemplate());
  };

  const handleSearch = (value) => {
    dispatch(setFilters({ name: value }));
  };

  const handleFilterChange = (key, value) => {
    dispatch(setFilters({ [key]: value }));
  };

  const handlePageChange = (page, size) => {
    dispatch(setPagination({ page, size }));
  };

  const handleResetFilters = () => {
    dispatch(resetFilters());
  };

  return (
    <div className="template-management">
      <Card title="模板管理" className="mb-4">
        {/* 搜索和筛选区域 */}
        <div className="mb-4">
          <Space wrap>
            <Search
              placeholder="搜索模板名称"
              allowClear
              style={{ width: 200 }}
              onSearch={handleSearch}
            />
            <Select
              placeholder="选择类型"
              style={{ width: 120 }}
              allowClear
              value={filters.type}
              onChange={(value) => handleFilterChange('type', value)}
            >
              <Option value="inquiry">询价</Option>
              <Option value="task">任务</Option>
              <Option value="field">字段</Option>
            </Select>
            <Select
              placeholder="选择范围"
              style={{ width: 120 }}
              allowClear
              value={filters.scope}
              onChange={(value) => handleFilterChange('scope', value)}
            >
              <Option value="private">私有</Option>
              <Option value="department">部门</Option>
              <Option value="company">公司</Option>
              <Option value="public">公开</Option>
            </Select>
            <Button onClick={handleResetFilters}>重置筛选</Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={handleCreateTemplate}
              loading={operations.creating}
            >
              新建模板
            </Button>
          </Space>
        </div>

        {/* 模板列表表格 */}
        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={templates}
            rowKey="id"
            pagination={false}
            size="middle"
          />
        </Spin>

        {/* 分页 */}
        <div className="mt-4 text-right">
          <Pagination
            current={pagination.page}
            pageSize={pagination.size}
            total={pagination.total}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }
            onChange={handlePageChange}
          />
        </div>
      </Card>

      {/* 模板详情/编辑模态框 */}
      <Modal
        title={
          modalType === 'create' ? '新建模板' : 
          modalType === 'edit' ? '编辑模板' : '模板详情'
        }
        open={isModalVisible}
        onOk={modalType !== 'view' ? handleModalOk : undefined}
        onCancel={handleModalCancel}
        width={800}
        footer={modalType === 'view' ? [
          <Button key="close" onClick={handleModalCancel}>
            关闭
          </Button>
        ] : undefined}
        confirmLoading={operations.creating || operations.updating}
      >
        <Form
          form={form}
          layout="vertical"
          disabled={modalType === 'view'}
        >
          <Form.Item
            name="name"
            label="模板名称"
            rules={[{ required: true, message: '请输入模板名称' }]}
          >
            <Input placeholder="请输入模板名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="模板描述"
          >
            <Input.TextArea placeholder="请输入模板描述" rows={3} />
          </Form.Item>
          
          <Form.Item
            name="type"
            label="模板类型"
            rules={[{ required: true, message: '请选择模板类型' }]}
          >
            <Select placeholder="请选择模板类型">
              <Option value="inquiry">询价模板</Option>
              <Option value="task">任务模板</Option>
              <Option value="field">字段模板</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="category"
            label="模板分类"
          >
            <Input placeholder="请输入模板分类" />
          </Form.Item>
          
          <Form.Item
            name="scope"
            label="共享范围"
            rules={[{ required: true, message: '请选择共享范围' }]}
          >
            <Select placeholder="请选择共享范围">
              <Option value="private">私有</Option>
              <Option value="department">部门</Option>
              <Option value="company">公司</Option>
              <Option value="public">公开</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TemplateExample;
