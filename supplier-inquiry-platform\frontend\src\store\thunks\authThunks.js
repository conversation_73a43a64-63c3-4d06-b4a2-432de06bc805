import { createAsyncThunk } from '@reduxjs/toolkit';
import authService from '../../services/authService';
import { loginStart, loginSuccess, loginFailure, logout as logoutAction } from '../slices/authSlice';

/**
 * 登录异步thunk
 */
export const login = createAsyncThunk(
  'auth/login',
  async ({ username, password }, { dispatch, rejectWithValue }) => {
    try {
      dispatch(loginStart());
      const response = await authService.login(username, password);

      // 保存token到localStorage
      localStorage.setItem('token', response.access_token);

      // 获取用户信息
      const user = await authService.getCurrentUser();

      dispatch(loginSuccess({
        token: response.access_token,
        user
      }));

      return { token: response.access_token, user };
    } catch (error) {
      const message = error.response?.data?.detail || '登录失败，请检查用户名和密码';
      dispatch(loginFailure(message));
      return rejectWithValue(message);
    }
  }
);

/**
 * 注册异步thunk
 */
export const register = createAsyncThunk(
  'auth/register',
  async (userData, { dispatch, rejectWithValue }) => {
    try {
      const response = await authService.register(userData);
      return response;
    } catch (error) {
      const message = error.response?.data?.detail || '注册失败，请稍后再试';
      return rejectWithValue(message);
    }
  }
);

/**
 * 供应商注册异步thunk
 */
export const registerSupplier = createAsyncThunk(
  'auth/registerSupplier',
  async (supplierData, { dispatch, rejectWithValue }) => {
    try {
      const response = await authService.registerSupplier(supplierData);
      return response;
    } catch (error) {
      const message = error.response?.data?.detail || '供应商注册失败，请稍后再试';
      return rejectWithValue(message);
    }
  }
);

/**
 * 登出异步thunk
 */
export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { dispatch }) => {
    try {
      await authService.logout();
      dispatch(logoutAction());
    } catch (error) {
      console.error('登出时发生错误:', error);
    }
  }
);

/**
 * 获取当前用户信息异步thunk
 */
export const getCurrentUser = createAsyncThunk(
  'auth/getCurrentUser',
  async (_, { dispatch, rejectWithValue }) => {
    try {
      dispatch(loginStart());
      const user = await authService.getCurrentUser();

      // 确保收到有效的用户数据
      if (!user || !user.id) {
        throw new Error('未获取到有效的用户数据');
      }

      dispatch(loginSuccess({
        token: localStorage.getItem('token'),
        user
      }));
      return user;
    } catch (error) {
      console.error('获取用户信息失败:', error);

      // 清除无效的token
      localStorage.removeItem('token');

      const message = error.response?.data?.detail || '获取用户信息失败';
      dispatch(loginFailure(message));
      return rejectWithValue(message);
    }
  }
);
