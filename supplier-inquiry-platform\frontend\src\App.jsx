import { Routes, Route, Navigate } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Layout } from 'antd'
import HomePage from './pages/HomePage'
import LoginPage from './pages/LoginPage'
import DashboardPage from './pages/DashboardPage'
import ProfilePage from './pages/ProfilePage'
import TasksPage from './pages/TasksPage'
import TaskDetailPage from './pages/TaskDetailPage'
import TaskCreatePage from './pages/TaskCreatePage'
import TaskEditPage from './pages/TaskEditPage'
import TaskSharePage from './pages/TaskSharePage'
import QuotesPage from './pages/QuotesPage'
import QuoteSubmitPage from './pages/QuoteSubmitPage'
import QuoteDetailPage from './pages/QuoteDetailPage'
import QuoteSummaryPage from './pages/QuoteSummaryPage'
import UsersPage from './pages/UsersPage'
import CompaniesPage from './pages/CompaniesPage'
import SystemConfigPage from './pages/SystemConfigPage'
import TemplateManagementPage from './pages/TemplateManagementPage'
import TeamPage from './pages/TeamPage'
import SettingsPage from './pages/SettingsPage'
import NotFoundPage from './pages/NotFoundPage'
import Navigation from './components/Navigation'
import ProtectedRoute from './components/ProtectedRoute'
import { getCurrentUser } from './store/thunks/authThunks'
import './App.css'

function App() {
  const dispatch = useDispatch()
  const { token, isAuthenticated } = useSelector((state) => state.auth)
  const [authChecked, setAuthChecked] = useState(false)

  // 如果有token但未认证，尝试获取用户信息
  useEffect(() => {
    const checkAuth = async () => {
      try {
    if (token && !isAuthenticated) {
          await dispatch(getCurrentUser()).unwrap()
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 当获取信息失败时，清除无效的token
        localStorage.removeItem('token')
      } finally {
        setAuthChecked(true)
      }
    }

    checkAuth()
  }, [dispatch, token, isAuthenticated])

  // 等待认证检查完成
  if (token && !isAuthenticated && !authChecked) {
    return <div>加载中...</div>
  }

  return (
    <Navigation>
      <Routes>
        {/* 公共路由 */}
        <Route path="/" element={<HomePage />} />
        <Route path="/login" element={<LoginPage />} />

        {/* 受保护的路由 */}
        <Route path="/dashboard" element={
          <ProtectedRoute>
            <DashboardPage />
          </ProtectedRoute>
        } />
        <Route path="/profile" element={
          <ProtectedRoute>
            <ProfilePage />
          </ProtectedRoute>
        } />

        {/* 任务相关路由 */}
        <Route path="/tasks" element={
          <ProtectedRoute>
            <TasksPage />
          </ProtectedRoute>
        } />
        <Route path="/tasks/create" element={
          <ProtectedRoute>
            <TaskCreatePage />
          </ProtectedRoute>
        } />
        <Route path="/tasks/:id" element={
          <ProtectedRoute>
            <TaskDetailPage />
          </ProtectedRoute>
        } />
        <Route path="/tasks/:id/edit" element={
          <ProtectedRoute>
            <TaskEditPage />
          </ProtectedRoute>
        } />

        {/* 报价相关路由 */}
        <Route path="/quotes" element={
          <ProtectedRoute>
            <QuotesPage />
          </ProtectedRoute>
        } />
        <Route path="/tasks/:taskId/quote" element={<QuoteSubmitPage />} />
        <Route path="/quotes/:quoteId" element={
          <ProtectedRoute>
            <QuoteDetailPage />
          </ProtectedRoute>
        } />
        <Route path="/quotes/:quoteId/edit" element={
          <ProtectedRoute>
            <QuoteDetailPage />
          </ProtectedRoute>
        } />
        <Route path="/tasks/:taskId/summary" element={
          <ProtectedRoute>
            <QuoteSummaryPage />
          </ProtectedRoute>
        } />

        {/* 用户管理路由 */}
        <Route path="/users" element={
          <ProtectedRoute requiredLevel={3}>
            <UsersPage />
          </ProtectedRoute>
        } />

        {/* 供应商管理路由 */}
        <Route path="/companies" element={
          <ProtectedRoute requiredLevel={4}>
            <CompaniesPage />
          </ProtectedRoute>
        } />

        {/* 系统配置管理路由 */}
        <Route path="/system-config" element={
          <ProtectedRoute requiredLevel={4}>
            <SystemConfigPage />
          </ProtectedRoute>
        } />

        {/* 模板管理路由 */}
        <Route path="/templates" element={
          <ProtectedRoute>
            <TemplateManagementPage />
          </ProtectedRoute>
        } />

        {/* 团队管理路由 */}
        <Route path="/team" element={
          <ProtectedRoute requiredLevel={2}>
            <TeamPage />
          </ProtectedRoute>
        } />

        {/* 设置页面路由 */}
        <Route path="/settings" element={
          <ProtectedRoute>
            <SettingsPage />
          </ProtectedRoute>
        } />

        {/* 任务分享路由 */}
        <Route path="/share/task/:taskId" element={<TaskSharePage />} />

        {/* 重定向 */}
        <Route path="/home" element={<Navigate to="/" replace />} />

        {/* 404页面 */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </Navigation>
  )
}

export default App
