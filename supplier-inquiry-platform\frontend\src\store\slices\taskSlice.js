import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import taskService from '../../services/taskService.js';

// 异步操作
export const fetchTasks = createAsyncThunk(
  'tasks/fetchTasks',
  async (params, { rejectWithValue }) => {
    try {
      return await taskService.getTasks(params);
    } catch (error) {
      return rejectWithValue(error.response?.data || '获取任务列表失败');
    }
  }
);

export const fetchTask = createAsyncThunk(
  'tasks/fetchTask',
  async (id, { rejectWithValue }) => {
    try {
      return await taskService.getTask(id);
    } catch (error) {
      return rejectWithValue(error.response?.data || '获取任务详情失败');
    }
  }
);

export const createTask = createAsyncThunk(
  'tasks/createTask',
  async (taskData, { rejectWithValue }) => {
    try {
      return await taskService.createTask(taskData);
    } catch (error) {
      return rejectWithValue(error.response?.data || '创建任务失败');
    }
  }
);

export const updateTask = createAsyncThunk(
  'tasks/updateTask',
  async ({ id, taskData }, { rejectWithValue }) => {
    try {
      return await taskService.updateTask(id, taskData);
    } catch (error) {
      return rejectWithValue(error.response?.data || '更新任务失败');
    }
  }
);

export const deleteTask = createAsyncThunk(
  'tasks/deleteTask',
  async (id, { rejectWithValue }) => {
    try {
      return await taskService.deleteTask(id);
    } catch (error) {
      return rejectWithValue(error.response?.data || '删除任务失败');
    }
  }
);

export const shareTask = createAsyncThunk(
  'tasks/shareTask',
  async (id, { rejectWithValue }) => {
    try {
      return await taskService.shareTask(id);
    } catch (error) {
      return rejectWithValue(error.response?.data || '生成分享链接失败');
    }
  }
);

// 初始状态
const initialState = {
  tasks: [],
  task: null,
  loading: false,
  error: null,
  shareLink: null,
};

// Slice
const taskSlice = createSlice({
  name: 'tasks',
  initialState,
  reducers: {
    clearTask: (state) => {
      state.task = null;
    },
    clearShareLink: (state) => {
      state.shareLink = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchTasks
      .addCase(fetchTasks.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTasks.fulfilled, (state, action) => {
        state.loading = false;
        state.tasks = action.payload;
      })
      .addCase(fetchTasks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || '获取任务列表失败';
      })
      // fetchTask
      .addCase(fetchTask.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTask.fulfilled, (state, action) => {
        state.loading = false;
        state.task = action.payload;
      })
      .addCase(fetchTask.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || '获取任务详情失败';
      })
      // createTask
      .addCase(createTask.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createTask.fulfilled, (state, action) => {
        state.loading = false;
        state.tasks.push(action.payload);
        state.task = action.payload;
      })
      .addCase(createTask.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || '创建任务失败';
      })
      // updateTask
      .addCase(updateTask.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateTask.fulfilled, (state, action) => {
        state.loading = false;
        state.task = action.payload;
        const index = state.tasks.findIndex(task => task.id === action.payload.id);
        if (index !== -1) {
          state.tasks[index] = action.payload;
        }
      })
      .addCase(updateTask.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || '更新任务失败';
      })
      // deleteTask
      .addCase(deleteTask.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteTask.fulfilled, (state, action) => {
        state.loading = false;
        state.tasks = state.tasks.filter(task => task.id !== action.meta.arg);
        if (state.task && state.task.id === action.meta.arg) {
          state.task = null;
        }
      })
      .addCase(deleteTask.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || '删除任务失败';
      })
      // shareTask
      .addCase(shareTask.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(shareTask.fulfilled, (state, action) => {
        state.loading = false;
        state.shareLink = action.payload.share_link;
      })
      .addCase(shareTask.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || '生成分享链接失败';
      });
  },
});

export const { clearTask, clearShareLink } = taskSlice.actions;

export default taskSlice.reducer;
