from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
import uuid

from app.api import deps
from app.models.permission import Permission
from app.models.user import User
from app.schemas.permission import Permission as PermissionSchema, PermissionCreate, PermissionUpdate

router = APIRouter()

@router.get("/", response_model=List[PermissionSchema])
def read_permissions(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取权限列表
    """
    # 权限检查
    if not deps.check_permission(current_user, "permission", "read", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    permissions = db.query(Permission).offset(skip).limit(limit).all()
    return permissions

@router.get("/{permission_id}", response_model=PermissionSchema)
def read_permission(
    permission_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取权限详情
    """
    # 权限检查
    if not deps.check_permission(current_user, "permission", "read", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    permission = db.query(Permission).filter(Permission.id == permission_id).first()
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="权限不存在",
        )
    return permission

@router.post("/", response_model=PermissionSchema)
def create_permission(
    *,
    db: Session = Depends(deps.get_db),
    permission_in: PermissionCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建新权限
    """
    # 权限检查
    if not deps.check_permission(current_user, "permission", "create", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 检查权限名是否已存在
    permission = db.query(Permission).filter(Permission.name == permission_in.name).first()
    if permission:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="权限名已存在",
        )

    # 创建新权限
    permission = Permission(**permission_in.dict())
    db.add(permission)
    db.commit()
    db.refresh(permission)
    return permission

@router.put("/{permission_id}", response_model=PermissionSchema)
def update_permission(
    *,
    db: Session = Depends(deps.get_db),
    permission_id: uuid.UUID,
    permission_in: PermissionUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新权限
    """
    # 权限检查
    if not deps.check_permission(current_user, "permission", "update", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    permission = db.query(Permission).filter(Permission.id == permission_id).first()
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="权限不存在",
        )

    # 更新权限
    update_data = permission_in.dict(exclude_unset=True)
    for field in update_data:
        setattr(permission, field, update_data[field])

    db.add(permission)
    db.commit()
    db.refresh(permission)
    return permission

@router.delete("/{permission_id}", response_model=PermissionSchema)
def delete_permission(
    *,
    db: Session = Depends(deps.get_db),
    permission_id: uuid.UUID,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除权限
    """
    # 权限检查
    if not deps.check_permission(current_user, "permission", "delete", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    permission = db.query(Permission).filter(Permission.id == permission_id).first()
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="权限不存在",
        )

    # 检查是否是默认权限
    if permission.name in [
        "task_read", "task_create", "task_update", "task_delete",
        "quote_read", "quote_create", "quote_update", "quote_delete",
        "user_read", "user_create", "user_update", "user_delete",
        "company_read", "company_create", "company_update", "company_delete",
        "role_read", "role_create", "role_update", "role_delete",
        "permission_read", "permission_create", "permission_update", "permission_delete",
        "template_read", "template_create", "template_update", "template_delete",
    ]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除默认权限",
        )

    db.delete(permission)
    db.commit()
    return permission
