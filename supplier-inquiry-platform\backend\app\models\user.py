from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import relationship
import uuid

from app.models.base import BaseModel
from app.db.custom_types import UUID
from app.models.user_role import user_role

class User(BaseModel):
    """用户模型"""

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    name = Column(String)
    phone = Column(String)
    company_id = Column(UUID(as_uuid=True), ForeignKey("company.id"))
    parent_id = Column(UUID(as_uuid=True), ForeignKey("user.id"))
    level = Column(Integer, nullable=False, default=1)  # 1: 普通用户, 2: 部门主管, 3: 分公司负责人, 4: 总部管理员, 5: 超级管理员
    is_active = Column(<PERSON><PERSON><PERSON>, default=True)

    # 关系
    company = relationship("Company", back_populates="users")
    parent = relationship("User", remote_side=[id], backref="subordinates")
    tasks = relationship("Task", back_populates="creator")
    quotes = relationship("Quote", back_populates="created_by")
    roles = relationship("Role", secondary=user_role, back_populates="users")
    templates = relationship("Template", back_populates="creator")
