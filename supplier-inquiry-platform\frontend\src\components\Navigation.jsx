import React, { useState } from 'react';
import { Layout, Menu, Button, Dropdown, Avatar } from 'antd';
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  UserOutlined,
  DashboardOutlined,
  FileOutlined,
  FileTextOutlined,
  ShoppingCartOutlined,
  ShopOutlined,
  TeamOutlined,
  SettingOutlined,
  LogoutOutlined
} from '@ant-design/icons';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { logout } from '../store/thunks/authThunks';

const { Header, Sider } = Layout;

const Navigation = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const { user, isAuthenticated } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
  };

  // 如果未登录，只显示内容，不显示导航
  if (!isAuthenticated) {
    return children;
  }

  // 用户菜单项
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: <Link to="/profile">个人资料</Link>,
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: <Link to="/settings">设置</Link>,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: <span onClick={handleLogout}>退出登录</span>,
    },
  ];

  // 侧边栏菜单项
  const sideMenuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: <Link to="/dashboard">控制台</Link>,
    },
    {
      key: 'tasks',
      icon: <FileOutlined />,
      label: <Link to="/tasks">任务管理</Link>,
    },
    {
      key: 'templates',
      icon: <FileTextOutlined />,
      label: <Link to="/templates">模板管理</Link>,
    },
    {
      key: 'quotes',
      icon: <ShoppingCartOutlined />,
      label: <Link to="/quotes">报价管理</Link>,
    },
  ];

  // 根据用户权限添加额外菜单项
  if (user?.level >= 2) {
    sideMenuItems.push({
      key: 'team',
      icon: <TeamOutlined />,
      label: <Link to="/team">团队管理</Link>,
    });
  }

  if (user?.level >= 3) {
    sideMenuItems.push({
      key: 'users',
      icon: <UserOutlined />,
      label: <Link to="/users">用户管理</Link>,
    });
  }

  if (user?.level >= 4) {
    sideMenuItems.push({
      key: 'companies',
      icon: <ShopOutlined />,
      label: <Link to="/companies">供应商管理</Link>,
    });
    sideMenuItems.push({
      key: 'system-config',
      icon: <SettingOutlined />,
      label: <Link to="/system-config">系统配置</Link>,
    });
  }

  // 获取当前选中的菜单项
  const getSelectedKey = () => {
    const path = location.pathname;
    if (path.startsWith('/dashboard')) return 'dashboard';
    if (path.startsWith('/tasks')) return 'tasks';
    if (path.startsWith('/templates')) return 'templates';
    if (path.startsWith('/quotes')) return 'quotes';
    if (path.startsWith('/team')) return 'team';
    if (path.startsWith('/users')) return 'users';
    if (path.startsWith('/companies')) return 'companies';
    if (path.startsWith('/system-config')) return 'system-config';
    return '';
  };

  return (
    <Layout className="min-h-screen">
      <Sider trigger={null} collapsible collapsed={collapsed} theme="light">
        <div className="p-4 h-16 flex items-center justify-center border-b border-gray-200">
          <h1 className={`text-lg font-bold ${collapsed ? 'hidden' : 'block'}`}>
            询价平台
          </h1>
          {collapsed && <span className="text-xl">询</span>}
        </div>
        <Menu
          mode="inline"
          selectedKeys={[getSelectedKey()]}
          items={sideMenuItems}
          className="border-r-0"
        />
      </Sider>
      <Layout>
        <Header className="bg-white p-0 flex justify-between items-center border-b border-gray-200">
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            className="w-16 h-16"
          />
          <div className="mr-4">
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <div className="flex items-center cursor-pointer">
                <Avatar icon={<UserOutlined />} />
                <span className="ml-2 mr-2">{user?.name || user?.username}</span>
              </div>
            </Dropdown>
          </div>
        </Header>
        {children}
      </Layout>
    </Layout>
  );
};

export default Navigation;
