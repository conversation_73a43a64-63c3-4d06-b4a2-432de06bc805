#!/usr/bin/env python3
"""
测试Template模型
"""
import sys
import os
sys.path.append(os.path.dirname(__file__))

try:
    from app.models.template import Template
    print("✓ Template模型导入成功")

    # 检查模型属性
    print("✓ Template模型属性:")
    print(f"  - 表名: {Template.__tablename__}")
    print(f"  - 列数: {len(Template.__table__.columns)}")

    # 列出所有列
    print("✓ Template模型字段:")
    for column in Template.__table__.columns:
        print(f"  - {column.name}: {column.type}")

    # 检查外键
    print("✓ Template模型外键:")
    for fk in Template.__table__.foreign_keys:
        print(f"  - {fk.parent.name} -> {fk.column}")

    print("\n✅ Template模型验证成功！")

except Exception as e:
    print(f"❌ Template模型验证失败: {e}")
    import traceback
    traceback.print_exc()
