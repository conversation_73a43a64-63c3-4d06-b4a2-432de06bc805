#!/usr/bin/env node

/**
 * Template Redux状态管理测试脚本
 * 用于验证templateSlice和templateService的基本功能
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 开始测试Template Redux状态管理');

// 测试文件是否存在
const testFiles = [
  'src/services/templateService.js',
  'src/store/slices/templateSlice.js',
  'src/store/index.js',
  'src/components/TemplateExample.jsx',
  'src/test/templateReduxTest.js'
];

console.log('\n📁 检查文件是否存在...');
testFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} - 存在`);
  } else {
    console.log(`❌ ${file} - 不存在`);
  }
});

// 检查文件内容
console.log('\n📄 检查文件内容...');

// 检查templateService.js
const templateServicePath = path.join(__dirname, 'src/services/templateService.js');
if (fs.existsSync(templateServicePath)) {
  const content = fs.readFileSync(templateServicePath, 'utf8');
  const requiredMethods = [
    'getTemplates',
    'getTemplate',
    'createTemplate',
    'updateTemplate',
    'deleteTemplate',
    'searchTemplatesByTags',
    'getCompanyTemplates',
    'copyTemplate'
  ];

  console.log('📋 templateService.js 方法检查:');
  requiredMethods.forEach(method => {
    if (content.includes(method)) {
      console.log(`  ✅ ${method} - 已实现`);
    } else {
      console.log(`  ❌ ${method} - 未实现`);
    }
  });
}

// 检查templateSlice.js
const templateSlicePath = path.join(__dirname, 'src/store/slices/templateSlice.js');
if (fs.existsSync(templateSlicePath)) {
  const content = fs.readFileSync(templateSlicePath, 'utf8');

  const asyncThunks = [
    'fetchTemplates',
    'fetchTemplate',
    'createTemplate',
    'updateTemplate',
    'deleteTemplate',
    'searchTemplatesByTags',
    'fetchCompanyTemplates',
    'copyTemplate'
  ];

  const syncActions = [
    'clearCurrentTemplate',
    'clearError',
    'setFilters',
    'resetFilters',
    'setPagination',
    'clearSearchResults',
    'clearCompanyTemplates'
  ];

  const selectors = [
    'selectTemplates',
    'selectCurrentTemplate',
    'selectTemplateLoading',
    'selectTemplateError',
    'selectTemplatePagination',
    'selectTemplateFilters',
    'selectSearchResults',
    'selectCompanyTemplates',
    'selectTemplateOperations'
  ];

  console.log('\n⚡ templateSlice.js 异步Actions检查:');
  asyncThunks.forEach(action => {
    if (content.includes(`export const ${action}`)) {
      console.log(`  ✅ ${action} - 已实现`);
    } else {
      console.log(`  ❌ ${action} - 未实现`);
    }
  });

  console.log('\n🔄 templateSlice.js 同步Actions检查:');
  syncActions.forEach(action => {
    if (content.includes(action)) {
      console.log(`  ✅ ${action} - 已实现`);
    } else {
      console.log(`  ❌ ${action} - 未实现`);
    }
  });

  console.log('\n🎯 templateSlice.js Selectors检查:');
  selectors.forEach(selector => {
    if (content.includes(`export const ${selector}`)) {
      console.log(`  ✅ ${selector} - 已实现`);
    } else {
      console.log(`  ❌ ${selector} - 未实现`);
    }
  });
}

// 检查store配置
const storePath = path.join(__dirname, 'src/store/index.js');
if (fs.existsSync(storePath)) {
  const content = fs.readFileSync(storePath, 'utf8');

  console.log('\n🏪 store配置检查:');
  if (content.includes('templateReducer')) {
    console.log('  ✅ templateReducer - 已导入');
  } else {
    console.log('  ❌ templateReducer - 未导入');
  }

  if (content.includes('templates: templateReducer')) {
    console.log('  ✅ templates reducer - 已注册');
  } else {
    console.log('  ❌ templates reducer - 未注册');
  }
}

// 检查组件示例
const componentPath = path.join(__dirname, 'src/components/TemplateExample.jsx');
if (fs.existsSync(componentPath)) {
  const content = fs.readFileSync(componentPath, 'utf8');

  console.log('\n🧩 TemplateExample.jsx 组件检查:');

  const requiredImports = [
    'useSelector',
    'useDispatch',
    'fetchTemplates',
    'selectTemplates',
    'selectTemplateLoading'
  ];

  requiredImports.forEach(importItem => {
    if (content.includes(importItem)) {
      console.log(`  ✅ ${importItem} - 已导入`);
    } else {
      console.log(`  ❌ ${importItem} - 未导入`);
    }
  });
}

// 生成使用指南
console.log('\n📖 Template Redux使用指南:');
console.log('');
console.log('1. 在组件中使用Redux状态:');
console.log('   ```javascript');
console.log('   import { useSelector, useDispatch } from "react-redux";');
console.log('   import { fetchTemplates, selectTemplates } from "../store/slices/templateSlice";');
console.log('   ');
console.log('   const MyComponent = () => {');
console.log('     const dispatch = useDispatch();');
console.log('     const templates = useSelector(selectTemplates);');
console.log('     ');
console.log('     useEffect(() => {');
console.log('       dispatch(fetchTemplates());');
console.log('     }, [dispatch]);');
console.log('   };');
console.log('   ```');
console.log('');
console.log('2. 可用的Actions:');
console.log('   - fetchTemplates(params) - 获取模板列表');
console.log('   - fetchTemplate(id) - 获取模板详情');
console.log('   - createTemplate(data) - 创建模板');
console.log('   - updateTemplate({id, data}) - 更新模板');
console.log('   - deleteTemplate(id) - 删除模板');
console.log('   - copyTemplate({id, newName}) - 复制模板');
console.log('   - setFilters(filters) - 设置筛选条件');
console.log('');
console.log('3. 可用的Selectors:');
console.log('   - selectTemplates - 模板列表');
console.log('   - selectCurrentTemplate - 当前模板');
console.log('   - selectTemplateLoading - 加载状态');
console.log('   - selectTemplateError - 错误信息');
console.log('   - selectTemplatePagination - 分页信息');
console.log('   - selectTemplateFilters - 筛选条件');
console.log('');
console.log('4. API端点映射:');
console.log('   - GET /templates - 获取模板列表');
console.log('   - GET /templates/{id} - 获取模板详情');
console.log('   - POST /templates - 创建模板');
console.log('   - PUT /templates/{id} - 更新模板');
console.log('   - DELETE /templates/{id} - 删除模板');
console.log('   - GET /templates/search/by-tags - 标签搜索');
console.log('   - GET /templates/company/{id} - 公司模板');

console.log('\n✅ Template Redux状态管理测试完成！');
console.log('\n🎯 下一步:');
console.log('1. 在实际组件中集成Template Redux状态管理');
console.log('2. 测试与后端API的集成');
console.log('3. 添加错误处理和用户反馈');
console.log('4. 优化性能和用户体验');
