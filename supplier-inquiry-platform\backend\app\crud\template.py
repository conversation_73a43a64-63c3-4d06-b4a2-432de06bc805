from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
import uuid

from app.models.template import Template
from app.models.user import User
from app.schemas.template import TemplateCreate, TemplateUpdate

def create_template(db: Session, template_in: TemplateCreate, creator_id: uuid.UUID) -> Template:
    """创建模板"""
    template_data = template_in.dict()
    template_data["creator_id"] = creator_id
    template_data["id"] = uuid.uuid4()
    
    template = Template(**template_data)
    db.add(template)
    db.commit()
    db.refresh(template)
    return template

def get_template(db: Session, template_id: uuid.UUID) -> Optional[Template]:
    """获取单个模板"""
    return db.query(Template).filter(Template.id == template_id).first()

def get_templates(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    name: Optional[str] = None,
    category: Optional[str] = None,
    type: Optional[str] = None,
    scope: Optional[str] = None,
    creator_id: Optional[uuid.UUID] = None,
    company_id: Optional[uuid.UUID] = None,
    is_active: Optional[bool] = True
) -> List[Template]:
    """获取模板列表，支持筛选"""
    query = db.query(Template)
    
    # 基础筛选
    if is_active is not None:
        query = query.filter(Template.is_active == is_active)
    
    # 按名称筛选
    if name:
        query = query.filter(Template.name.ilike(f"%{name}%"))
    
    # 按分类筛选
    if category:
        query = query.filter(Template.category == category)
    
    # 按类型筛选
    if type:
        query = query.filter(Template.type == type)
    
    # 按范围筛选
    if scope:
        query = query.filter(Template.scope == scope)
    
    # 按创建者筛选
    if creator_id:
        query = query.filter(Template.creator_id == creator_id)
    
    # 按公司筛选
    if company_id:
        query = query.filter(Template.company_id == company_id)
    
    return query.offset(skip).limit(limit).all()

def get_templates_count(
    db: Session,
    name: Optional[str] = None,
    category: Optional[str] = None,
    type: Optional[str] = None,
    scope: Optional[str] = None,
    creator_id: Optional[uuid.UUID] = None,
    company_id: Optional[uuid.UUID] = None,
    is_active: Optional[bool] = True
) -> int:
    """获取模板总数，支持筛选"""
    query = db.query(Template)
    
    # 基础筛选
    if is_active is not None:
        query = query.filter(Template.is_active == is_active)
    
    # 按名称筛选
    if name:
        query = query.filter(Template.name.ilike(f"%{name}%"))
    
    # 按分类筛选
    if category:
        query = query.filter(Template.category == category)
    
    # 按类型筛选
    if type:
        query = query.filter(Template.type == type)
    
    # 按范围筛选
    if scope:
        query = query.filter(Template.scope == scope)
    
    # 按创建者筛选
    if creator_id:
        query = query.filter(Template.creator_id == creator_id)
    
    # 按公司筛选
    if company_id:
        query = query.filter(Template.company_id == company_id)
    
    return query.count()

def get_templates_by_company(db: Session, company_id: uuid.UUID, skip: int = 0, limit: int = 100) -> List[Template]:
    """获取公司的模板列表"""
    return db.query(Template).filter(
        and_(
            Template.company_id == company_id,
            Template.is_active == True
        )
    ).offset(skip).limit(limit).all()

def get_accessible_templates(
    db: Session,
    user: User,
    skip: int = 0,
    limit: int = 100,
    name: Optional[str] = None,
    category: Optional[str] = None,
    type: Optional[str] = None
) -> List[Template]:
    """获取用户可访问的模板列表"""
    query = db.query(Template).filter(Template.is_active == True)
    
    # 根据用户权限级别筛选
    if user.level >= 5:  # 超级管理员：所有模板
        pass
    elif user.level >= 4:  # 总部管理员：所有模板
        pass
    elif user.level >= 3:  # 分公司负责人：本企业模板
        query = query.filter(
            or_(
                Template.scope == "public",
                and_(Template.company_id == user.company_id, Template.scope.in_(["company", "department"])),
                Template.creator_id == user.id
            )
        )
    elif user.level >= 2:  # 部门主管：本部门模板
        query = query.filter(
            or_(
                Template.scope == "public",
                and_(Template.company_id == user.company_id, Template.scope == "company"),
                Template.creator_id == user.id
            )
        )
    else:  # 普通用户：授权模板
        query = query.filter(
            or_(
                Template.scope == "public",
                Template.creator_id == user.id
            )
        )
    
    # 应用筛选条件
    if name:
        query = query.filter(Template.name.ilike(f"%{name}%"))
    if category:
        query = query.filter(Template.category == category)
    if type:
        query = query.filter(Template.type == type)
    
    return query.offset(skip).limit(limit).all()

def update_template(db: Session, template: Template, template_in: TemplateUpdate) -> Template:
    """更新模板"""
    update_data = template_in.dict(exclude_unset=True)
    for field in update_data:
        setattr(template, field, update_data[field])
    
    db.add(template)
    db.commit()
    db.refresh(template)
    return template

def delete_template(db: Session, template: Template) -> Template:
    """删除模板（软删除）"""
    template.is_active = False
    db.add(template)
    db.commit()
    db.refresh(template)
    return template

def search_templates_by_tags(db: Session, tags: List[str], user: User) -> List[Template]:
    """根据标签搜索模板"""
    # 这里需要根据具体的JSON查询语法来实现
    # SQLite的JSON查询语法与PostgreSQL不同
    # 暂时使用简单的文本搜索
    query = db.query(Template).filter(Template.is_active == True)
    
    # 根据用户权限筛选
    if user.level < 4:
        if user.level >= 3:
            query = query.filter(
                or_(
                    Template.scope == "public",
                    and_(Template.company_id == user.company_id, Template.scope.in_(["company", "department"])),
                    Template.creator_id == user.id
                )
            )
        elif user.level >= 2:
            query = query.filter(
                or_(
                    Template.scope == "public",
                    and_(Template.company_id == user.company_id, Template.scope == "company"),
                    Template.creator_id == user.id
                )
            )
        else:
            query = query.filter(
                or_(
                    Template.scope == "public",
                    Template.creator_id == user.id
                )
            )
    
    return query.all()
