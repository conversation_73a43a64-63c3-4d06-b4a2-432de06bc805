#!/usr/bin/env python3
"""
测试Template API接口
"""
import sys
import os
sys.path.append(os.path.dirname(__file__))

try:
    # 测试导入
    from app.schemas.template import TemplateCreate, TemplateUpdate, Template, TemplateResponse, TemplateList
    from app.crud.template import create_template, get_template, get_templates, update_template, delete_template
    from app.api.endpoints.templates import router
    from app.models.template import Template as TemplateModel
    
    print("✅ 所有Template相关模块导入成功")
    
    # 测试Schema
    print("\n📋 测试Pydantic模型:")
    
    # 测试TemplateCreate
    template_create_data = {
        "name": "测试模板",
        "description": "这是一个测试模板",
        "type": "inquiry",
        "category": "测试分类",
        "tags": {"tag1": "value1", "tag2": "value2"},
        "content": {"field1": "value1", "field2": "value2"},
        "scope": "private"
    }
    
    template_create = TemplateCreate(**template_create_data)
    print(f"  ✓ TemplateCreate: {template_create.name}")
    
    # 测试TemplateUpdate
    template_update = TemplateUpdate(name="更新的模板名称")
    print(f"  ✓ TemplateUpdate: {template_update.name}")
    
    # 测试API路由
    print("\n🌐 测试API路由:")
    print(f"  ✓ Router创建成功，包含 {len(router.routes)} 个路由")
    
    # 列出所有路由
    for route in router.routes:
        if hasattr(route, 'methods') and hasattr(route, 'path'):
            methods = ', '.join(route.methods)
            print(f"    - {methods} {route.path}")
    
    # 测试数据库模型
    print("\n🗄️ 测试数据库模型:")
    print(f"  ✓ Template模型表名: {TemplateModel.__tablename__}")
    print(f"  ✓ Template模型字段数: {len(TemplateModel.__table__.columns)}")
    
    # 测试CRUD函数签名
    print("\n🔧 测试CRUD函数:")
    crud_functions = [
        "create_template",
        "get_template", 
        "get_templates",
        "update_template",
        "delete_template"
    ]
    
    for func_name in crud_functions:
        if func_name in globals():
            print(f"  ✓ {func_name} 函数存在")
        else:
            print(f"  ❌ {func_name} 函数不存在")
    
    print("\n✅ Template API接口测试完成！")
    print("\n📝 API端点总结:")
    print("  - GET /templates/ - 获取模板列表")
    print("  - GET /templates/{id} - 获取模板详情")
    print("  - POST /templates/ - 创建模板")
    print("  - PUT /templates/{id} - 更新模板")
    print("  - DELETE /templates/{id} - 删除模板")
    print("  - GET /templates/search/by-tags - 根据标签搜索")
    print("  - GET /templates/company/{company_id} - 获取公司模板")
    
except Exception as e:
    print(f"❌ Template API测试失败: {e}")
    import traceback
    traceback.print_exc()
