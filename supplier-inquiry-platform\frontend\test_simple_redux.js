/**
 * 简单的Redux测试
 */

console.log('🧪 开始简单Redux测试');

try {
  // 测试基本导入
  console.log('📦 测试模块导入...');
  
  // 动态导入测试
  Promise.all([
    import('./src/store/slices/templateSlice.js'),
    import('./src/services/templateService.js'),
    import('./src/store/index.js')
  ]).then(([templateSliceModule, templateServiceModule, storeModule]) => {
    console.log('✅ 所有模块导入成功');
    
    // 测试templateService
    console.log('\n🔧 测试templateService:');
    const service = templateServiceModule.templateService;
    const serviceMethods = Object.keys(service);
    console.log(`  - 可用方法数量: ${serviceMethods.length}`);
    serviceMethods.forEach(method => {
      console.log(`  - ${method}: ${typeof service[method] === 'function' ? '✅' : '❌'}`);
    });
    
    // 测试templateSlice
    console.log('\n⚡ 测试templateSlice:');
    const asyncActions = [
      'fetchTemplates', 'fetchTemplate', 'createTemplate', 
      'updateTemplate', 'deleteTemplate', 'searchTemplatesByTags',
      'fetchCompanyTemplates', 'copyTemplate'
    ];
    
    asyncActions.forEach(action => {
      if (templateSliceModule[action]) {
        console.log(`  - ${action}: ✅`);
      } else {
        console.log(`  - ${action}: ❌`);
      }
    });
    
    const selectors = [
      'selectTemplates', 'selectCurrentTemplate', 'selectTemplateLoading',
      'selectTemplateError', 'selectTemplatePagination', 'selectTemplateFilters'
    ];
    
    console.log('\n🎯 测试selectors:');
    selectors.forEach(selector => {
      if (templateSliceModule[selector]) {
        console.log(`  - ${selector}: ✅`);
      } else {
        console.log(`  - ${selector}: ❌`);
      }
    });
    
    // 测试store配置
    console.log('\n🏪 测试store配置:');
    const store = storeModule.default;
    const state = store.getState();
    console.log(`  - store创建: ✅`);
    console.log(`  - templates reducer: ${state.templates ? '✅' : '❌'}`);
    console.log(`  - 初始状态结构: ${JSON.stringify(Object.keys(state.templates), null, 2)}`);
    
    // 测试基本Redux操作
    console.log('\n🔄 测试基本Redux操作:');
    
    // 测试同步action
    const { setFilters, clearCurrentTemplate } = templateSliceModule;
    
    store.dispatch(setFilters({ name: '测试模板', type: 'inquiry' }));
    const stateAfterFilter = store.getState();
    console.log(`  - setFilters: ${stateAfterFilter.templates.filters.name === '测试模板' ? '✅' : '❌'}`);
    
    store.dispatch(clearCurrentTemplate());
    const stateAfterClear = store.getState();
    console.log(`  - clearCurrentTemplate: ${stateAfterClear.templates.currentTemplate === null ? '✅' : '❌'}`);
    
    console.log('\n✅ 所有测试完成！');
    console.log('\n📋 测试总结:');
    console.log('  ✅ 模块导入正常');
    console.log('  ✅ templateService方法完整');
    console.log('  ✅ templateSlice actions完整');
    console.log('  ✅ selectors功能正常');
    console.log('  ✅ store配置正确');
    console.log('  ✅ Redux操作正常');
    
  }).catch(error => {
    console.error('❌ 模块导入失败:', error);
  });
  
} catch (error) {
  console.error('❌ 测试失败:', error);
}
