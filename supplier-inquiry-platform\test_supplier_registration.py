#!/usr/bin/env python3
"""
测试供应商注册功能
"""
import requests
import json

BASE_URL = "http://localhost:5000/api"

def test_registration_config():
    """测试注册配置API"""
    print("=== 测试注册配置API ===")
    try:
        response = requests.get(f"{BASE_URL}/system-config/public/registration-config")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"配置数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_supplier_registration():
    """测试供应商注册API"""
    print("\n=== 测试供应商注册API ===")
    
    supplier_data = {
        "username": "test_supplier_001",
        "email": "<EMAIL>",
        "password": "password123",
        "name": "测试供应商001",
        "phone": "13800138001",
        "company_name": "测试供应商公司001",
        "company_address": "北京市朝阳区测试街道001号",
        "contact_person": "张三",
        "contact_phone": "13800138001",
        "contact_email": "<EMAIL>"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/auth/register-supplier",
            json=supplier_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"注册成功: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"注册失败: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_normal_user_registration():
    """测试普通用户注册API"""
    print("\n=== 测试普通用户注册API ===")
    
    user_data = {
        "username": "test_user_001",
        "email": "<EMAIL>",
        "password": "password123",
        "name": "测试用户001",
        "phone": "13800138002",
        "level": 1,
        "is_active": True
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/auth/register",
            json=user_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"注册成功: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"注册失败: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    print("开始测试供应商注册功能...")
    
    # 测试注册配置
    test_registration_config()
    
    # 测试供应商注册
    test_supplier_registration()
    
    # 测试普通用户注册
    test_normal_user_registration()
    
    print("\n测试完成！")
