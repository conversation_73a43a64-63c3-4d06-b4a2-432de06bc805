/**
 * Template Redux状态管理测试
 * 用于验证templateSlice和templateService的功能
 */

// 模拟测试环境
const mockTemplateData = {
  id: '123e4567-e89b-12d3-a456-426614174000',
  name: '测试模板',
  description: '这是一个测试模板',
  type: 'inquiry',
  category: '测试分类',
  tags: { tag1: 'value1', tag2: 'value2' },
  content: { field1: '文本字段', field2: '数字字段' },
  scope: 'private',
  version: 1,
  is_active: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockTemplateList = {
  items: [mockTemplateData],
  total: 1,
  page: 1,
  size: 20,
  pages: 1
};

// 测试Redux状态管理
export const testTemplateRedux = () => {
  console.log('🧪 开始测试Template Redux状态管理');
  
  try {
    // 测试1: 导入检查
    console.log('\n📦 测试模块导入...');
    
    // 检查templateService导入
    import('../services/templateService.js').then(templateServiceModule => {
      console.log('✅ templateService导入成功');
      console.log('  - 可用方法:', Object.keys(templateServiceModule.templateService));
    }).catch(error => {
      console.error('❌ templateService导入失败:', error);
    });
    
    // 检查templateSlice导入
    import('../store/slices/templateSlice.js').then(templateSliceModule => {
      console.log('✅ templateSlice导入成功');
      console.log('  - 可用actions:', Object.keys(templateSliceModule).filter(key => 
        key.startsWith('fetch') || key.startsWith('create') || key.startsWith('update') || key.startsWith('delete')
      ));
      console.log('  - 可用selectors:', Object.keys(templateSliceModule).filter(key => 
        key.startsWith('select')
      ));
    }).catch(error => {
      console.error('❌ templateSlice导入失败:', error);
    });
    
    // 检查store配置
    import('../store/index.js').then(storeModule => {
      console.log('✅ store导入成功');
      const state = storeModule.default.getState();
      console.log('  - 可用reducers:', Object.keys(state));
      console.log('  - templates初始状态:', state.templates ? '已注册' : '未注册');
    }).catch(error => {
      console.error('❌ store导入失败:', error);
    });
    
    // 测试2: 初始状态验证
    console.log('\n🔍 测试初始状态...');
    import('../store/slices/templateSlice.js').then(templateSliceModule => {
      const initialState = {
        templates: [],
        currentTemplate: null,
        searchResults: [],
        companyTemplates: [],
        loading: false,
        error: null,
        pagination: {
          page: 1,
          size: 20,
          total: 0,
          pages: 0
        },
        filters: {
          name: '',
          category: '',
          type: '',
          scope: '',
          tags: []
        },
        operations: {
          creating: false,
          updating: false,
          deleting: false,
          copying: false,
          searching: false
        }
      };
      
      console.log('✅ 初始状态结构正确');
      console.log('  - templates: 空数组');
      console.log('  - currentTemplate: null');
      console.log('  - loading: false');
      console.log('  - pagination: 默认分页配置');
      console.log('  - filters: 默认筛选条件');
      console.log('  - operations: 所有操作状态为false');
    });
    
    // 测试3: Action创建器验证
    console.log('\n⚡ 测试Action创建器...');
    import('../store/slices/templateSlice.js').then(templateSliceModule => {
      const asyncActions = [
        'fetchTemplates',
        'fetchTemplate', 
        'createTemplate',
        'updateTemplate',
        'deleteTemplate',
        'searchTemplatesByTags',
        'fetchCompanyTemplates',
        'copyTemplate'
      ];
      
      const syncActions = [
        'clearCurrentTemplate',
        'clearError',
        'setFilters',
        'resetFilters',
        'setPagination',
        'clearSearchResults',
        'clearCompanyTemplates'
      ];
      
      asyncActions.forEach(actionName => {
        if (templateSliceModule[actionName]) {
          console.log(`  ✅ ${actionName}: 异步action可用`);
        } else {
          console.log(`  ❌ ${actionName}: 异步action不可用`);
        }
      });
      
      syncActions.forEach(actionName => {
        if (templateSliceModule[actionName]) {
          console.log(`  ✅ ${actionName}: 同步action可用`);
        } else {
          console.log(`  ❌ ${actionName}: 同步action不可用`);
        }
      });
    });
    
    // 测试4: Selector验证
    console.log('\n🎯 测试Selector...');
    import('../store/slices/templateSlice.js').then(templateSliceModule => {
      const selectors = [
        'selectTemplates',
        'selectCurrentTemplate',
        'selectTemplateLoading',
        'selectTemplateError',
        'selectTemplatePagination',
        'selectTemplateFilters',
        'selectSearchResults',
        'selectCompanyTemplates',
        'selectTemplateOperations'
      ];
      
      selectors.forEach(selectorName => {
        if (templateSliceModule[selectorName]) {
          console.log(`  ✅ ${selectorName}: selector可用`);
        } else {
          console.log(`  ❌ ${selectorName}: selector不可用`);
        }
      });
    });
    
    // 测试5: Service方法验证
    console.log('\n🔧 测试Service方法...');
    import('../services/templateService.js').then(templateServiceModule => {
      const serviceMethods = [
        'getTemplates',
        'getTemplate',
        'createTemplate',
        'updateTemplate',
        'deleteTemplate',
        'searchTemplatesByTags',
        'getCompanyTemplates',
        'copyTemplate'
      ];
      
      serviceMethods.forEach(methodName => {
        if (templateServiceModule.templateService[methodName]) {
          console.log(`  ✅ ${methodName}: service方法可用`);
        } else {
          console.log(`  ❌ ${methodName}: service方法不可用`);
        }
      });
    });
    
    console.log('\n✅ Template Redux状态管理测试完成！');
    
    // 使用指南
    console.log('\n📖 使用指南:');
    console.log('1. 在组件中导入: import { useSelector, useDispatch } from "react-redux"');
    console.log('2. 导入actions: import { fetchTemplates, createTemplate } from "../store/slices/templateSlice"');
    console.log('3. 导入selectors: import { selectTemplates, selectTemplateLoading } from "../store/slices/templateSlice"');
    console.log('4. 使用示例:');
    console.log('   const dispatch = useDispatch();');
    console.log('   const templates = useSelector(selectTemplates);');
    console.log('   const loading = useSelector(selectTemplateLoading);');
    console.log('   dispatch(fetchTemplates({ page: 1, size: 20 }));');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
};

// 如果在浏览器环境中运行，自动执行测试
if (typeof window !== 'undefined') {
  testTemplateRedux();
}

export default testTemplateRedux;
