from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, Text
from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship
import uuid

from app.models.base import BaseModel
from app.db.custom_types import UUID

class Template(BaseModel):
    """模板模型"""

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    type = Column(String(50), nullable=False)  # inquiry, task, field
    category = Column(String(50))
    tags = Column(JSON)
    content = Column(JSON, nullable=False)
    version = Column(Integer, nullable=False, default=1)
    scope = Column(String(50), nullable=False, default="private")  # private, department, company, public
    creator_id = Column(UUID(as_uuid=True), ForeignKey("user.id"), nullable=False)
    company_id = Column(UUID(as_uuid=True), Foreign<PERSON>ey("company.id"))
    parent_id = Column(UUID(as_uuid=True), ForeignKey("template.id"))
    is_active = Column(Boolean, default=True)

    # 关系
    creator = relationship("User", back_populates="templates")
    company = relationship("Company", back_populates="templates")
    parent = relationship("Template", remote_side=[id], backref="children")
