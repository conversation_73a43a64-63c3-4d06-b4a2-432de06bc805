/* 模板管理页面样式 */

.template-management {
  padding: 24px;
}

.template-management .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.template-management .ant-table {
  background: white;
}

.template-management .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.template-management .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 搜索和筛选区域样式 */
.template-filters {
  margin-bottom: 16px;
}

.template-filters .ant-input-search {
  border-radius: 6px;
}

.template-filters .ant-select {
  border-radius: 6px;
}

/* 操作按钮样式 */
.template-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.template-actions .ant-btn {
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.template-actions .ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.template-actions .ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

.template-actions .ant-btn-danger {
  background: #ff4d4f;
  border-color: #ff4d4f;
  color: white;
}

.template-actions .ant-btn-danger:hover {
  background: #ff7875;
  border-color: #ff7875;
}

/* 标签样式 */
.template-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

.template-tag.type-inquiry {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.template-tag.type-task {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.template-tag.type-field {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.template-tag.scope-private {
  background: #fff1f0;
  color: #ff4d4f;
  border: 1px solid #ffa39e;
}

.template-tag.scope-department {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.template-tag.scope-company {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.template-tag.scope-public {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

/* 页面标题样式 */
.template-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.template-page-header h2 {
  margin: 0;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.template-page-header .anticon {
  margin-right: 8px;
  color: #1890ff;
}

/* 批量操作样式 */
.template-batch-actions {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f0f2f5;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.template-batch-actions .batch-info {
  color: #595959;
  font-size: 14px;
}

.template-batch-actions .batch-buttons {
  display: flex;
  gap: 8px;
}

/* 空状态样式 */
.template-empty {
  text-align: center;
  padding: 48px 24px;
  color: #8c8c8c;
}

.template-empty .anticon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.template-empty h3 {
  color: #595959;
  font-size: 16px;
  margin-bottom: 8px;
}

.template-empty p {
  color: #8c8c8c;
  font-size: 14px;
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-management {
    padding: 16px;
  }
  
  .template-page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .template-filters {
    flex-direction: column;
    gap: 12px;
  }
  
  .template-filters .ant-col {
    width: 100% !important;
  }
  
  .template-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .template-actions .ant-btn {
    width: 100%;
    justify-content: center;
  }
  
  .template-batch-actions {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .template-management {
    padding: 12px;
  }
  
  .template-page-header h2 {
    font-size: 20px;
  }
  
  .ant-table {
    font-size: 12px;
  }
  
  .template-actions .ant-btn {
    padding: 4px 8px;
    font-size: 12px;
  }
}

/* 加载状态样式 */
.template-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 48px;
}

.template-loading .ant-spin {
  font-size: 24px;
}

/* 错误状态样式 */
.template-error {
  text-align: center;
  padding: 48px 24px;
  color: #ff4d4f;
}

.template-error .anticon {
  font-size: 48px;
  color: #ff4d4f;
  margin-bottom: 16px;
}

.template-error h3 {
  color: #ff4d4f;
  font-size: 16px;
  margin-bottom: 8px;
}

.template-error p {
  color: #8c8c8c;
  font-size: 14px;
  margin-bottom: 16px;
}

/* 动画效果 */
.template-fade-in {
  animation: templateFadeIn 0.3s ease-in-out;
}

@keyframes templateFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.template-slide-up {
  animation: templateSlideUp 0.3s ease-in-out;
}

@keyframes templateSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
