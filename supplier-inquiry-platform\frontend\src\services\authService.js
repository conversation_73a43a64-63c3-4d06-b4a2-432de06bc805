import api from './api';

const authService = {
  login: async (username, password) => {
    try {
      // 使用表单格式发送请求，而不是JSON
      const formData = new URLSearchParams();
      formData.append('username', username);
      formData.append('password', password);

      const response = await api.post('/auth/login', formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
      return response;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  },

  register: async (userData) => {
    try {
      const response = await api.post('/auth/register', userData);
      return response;
    } catch (error) {
      console.error('注册失败:', error);
      throw error;
    }
  },

  registerSupplier: async (supplierData) => {
    try {
      const response = await api.post('/auth/register-supplier', supplierData);
      return response;
    } catch (error) {
      console.error('供应商注册失败:', error);
      throw error;
    }
  },

  logout: () => {
    try {
    localStorage.removeItem('token');
      return true;
    } catch (error) {
      console.error('登出操作失败:', error);
      return false;
    }
  },

  getCurrentUser: async () => {
    try {
      // 检查是否有token，如果没有则不发送请求
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未找到认证令牌');
      }

      const response = await api.get('/auth/me');
      return response;
    } catch (error) {
      console.error('获取用户信息失败:', error);

      // 处理特定错误
      if (error.response) {
        if (error.response.status === 404 || error.response.status === 401) {
          console.warn('用户未登录或会话已过期');
          localStorage.removeItem('token');
        }
      }

      throw error;
    }
  },

  // 检查令牌是否有效
  verifyToken: async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        return false;
      }

      // 简单地尝试获取用户信息来验证令牌
      await authService.getCurrentUser();
      return true;
    } catch (error) {
      console.warn('令牌验证失败:', error);
      return false;
    }
  }
};

export default authService;
